name: E2E Tests

on:
  push:
    branches: [ main, master, develop ]
  pull_request:
    branches: [ main, master, develop ]
  workflow_dispatch:

jobs:
  e2e-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    

    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Get Playwright version
      id: playwright-version
      working-directory: ./frontend
      run: echo "version=$(npx playwright --version | cut -d' ' -f2)" >> "$GITHUB_OUTPUT"

    - name: Cache Playwright browsers
      uses: actions/cache@v3
      with:
        path: ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.version }}

    - name: Install Playwright Browsers
      working-directory: ./frontend
      run: npx playwright install --with-deps
    
    - name: Setup test database
      working-directory: ./backend
      run: |
        python init_db.py
        python seed_data.py
    
    - name: Start backend server
      working-directory: ./backend
      run: |
        python app.py &
        sleep 10
        # Verify backend is running
        curl -f http://localhost:5000/api/health || exit 1
    
    - name: Build frontend
      working-directory: ./frontend
      run: npm run build
    
    - name: Start frontend server
      working-directory: ./frontend
      run: |
        npm run preview &
        sleep 10
        # Verify frontend is running
        curl -f http://localhost:4173 || exit 1
    
    - name: Run Playwright tests
      working-directory: ./frontend
      run: npx playwright test
      env:
        PLAYWRIGHT_BASE_URL: http://localhost:4173
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report
        path: frontend/playwright-report/
        retention-days: 30
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results
        path: frontend/test-results/
        retention-days: 30

    - name: Post Playwright summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          const resultsPath = 'frontend/test-results/results.json';
          let body = '';
          if (fs.existsSync(resultsPath)) {
            const data = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
            const total = data.total || 0;
            const passed = data.passed || 0;
            const failed = data.failed || 0;
            body += `### Playwright Test Summary\n`;
            body += `- **Passed:** ${passed}\n`;
            body += `- **Failed:** ${failed}\n`;
            body += `- **Total:** ${total}\n`;
          } else {
            body = 'Playwright results not found.';
          }
          const runUrl = `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}#artifacts`;
          body += `\n[Artifacts](${runUrl})`;
          await github.rest.issues.createComment({
            ...context.repo,
            issue_number: context.issue.number,
            body
          });

  # Run tests on multiple browsers
  cross-browser-tests:
    timeout-minutes: 60
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        browser: [chromium, firefox, webkit]
    
    steps:
    - uses: actions/checkout@v4
    
    - uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'
        cache-dependency-path: backend/requirements.txt
    
    - name: Install frontend dependencies
      working-directory: ./frontend
      run: npm ci
    
    - name: Install backend dependencies
      working-directory: ./backend
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Get Playwright version
      id: playwright-version
      working-directory: ./frontend
      run: echo "version=$(npx playwright --version | cut -d' ' -f2)" >> "$GITHUB_OUTPUT"

    - name: Cache Playwright browsers
      uses: actions/cache@v3
      with:
        path: ~/.cache/ms-playwright
        key: ${{ runner.os }}-playwright-${{ steps.playwright-version.outputs.version }}

    - name: Install Playwright Browsers
      working-directory: ./frontend
      run: npx playwright install --with-deps ${{ matrix.browser }}
    
    - name: Setup test database
      working-directory: ./backend
      run: |
        python init_db.py
        python seed_data.py
    
    - name: Start backend server
      working-directory: ./backend
      run: |
        python app.py &
        sleep 10
        curl -f http://localhost:5000/api/health || exit 1
    
    - name: Build and start frontend
      working-directory: ./frontend
      run: |
        npm run build
        npm run preview &
        sleep 10
        curl -f http://localhost:4173 || exit 1
    
    - name: Run Playwright tests on ${{ matrix.browser }}
      working-directory: ./frontend
      run: npx playwright test --project=${{ matrix.browser }}
      env:
        PLAYWRIGHT_BASE_URL: http://localhost:4173
    
    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: playwright-report-${{ matrix.browser }}
        path: frontend/playwright-report/
        retention-days: 30

    - uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.browser }}
        path: frontend/test-results/
        retention-days: 30

    - name: Post Playwright summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      env:
        BROWSER: ${{ matrix.browser }}
      with:
        script: |
          const fs = require('fs');
          const resultsPath = 'frontend/test-results/results.json';
          let body = '';
          if (fs.existsSync(resultsPath)) {
            const data = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
            const total = data.total || 0;
            const passed = data.passed || 0;
            const failed = data.failed || 0;
            body += `### Playwright Test Summary (${process.env.BROWSER})\n`;
            body += `- **Passed:** ${passed}\n`;
            body += `- **Failed:** ${failed}\n`;
            body += `- **Total:** ${total}\n`;
          } else {
            body = 'Playwright results not found.';
          }
          const runUrl = `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}#artifacts`;
          body += `\n[Artifacts](${runUrl})`;
          await github.rest.issues.createComment({
            ...context.repo,
            issue_number: context.issue.number,
            body
          });
