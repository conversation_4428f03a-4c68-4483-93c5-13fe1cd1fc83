import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  testDir: './tests/e2e',
  testIgnore: process.env.RUN_PRODUCTION_TESTS === 'true' ? [] : ['**/production/**'],
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }]
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:5173',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Record video on failure */
    video: 'retain-on-failure',

    /* Global timeout for each action */
    actionTimeout: 10000,

    /* Global timeout for navigation */
    navigationTimeout: 30000,

    /* Ignore HTTPS errors */
    ignoreHTTPSErrors: true,

    /* Extra HTTP headers */
    extraHTTPHeaders: {
      'Accept-Language': 'en-US,en;q=0.9'
    },

    /* Emulate timezone */
    timezoneId: 'America/New_York',

    /* Emulate locale */
    locale: 'en-US',
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\.spec\.js$/,
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
      testMatch: /.*\.spec\.js$/,
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
      testMatch: /.*\.spec\.js$/,
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
      testMatch: /.*\/(mobile|auth|dashboard)\/.*\.spec\.js$/,
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
      testMatch: /.*\/(mobile|auth|dashboard)\/.*\.spec\.js$/,
    },

    /* Test against branded browsers. */
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Chrome'], channel: 'msedge' },
      testMatch: /.*\/(auth|dashboard|tools)\/.*\.spec\.js$/,
    },
    {
      name: 'Google Chrome',
      use: { ...devices['Desktop Chrome'], channel: 'chrome' },
      testMatch: /.*\.spec\.js$/,
    },

    /* Performance testing project */
    {
      name: 'performance',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\/performance\/.*\.spec\.js$/,
      timeout: 60000, // Longer timeout for performance tests
    },

    /* Visual regression testing project */
    {
      name: 'visual',
      use: {
        ...devices['Desktop Chrome'],
        // Consistent viewport for visual tests
        viewport: { width: 1280, height: 720 }
      },
      testMatch: /.*\/visual\/.*\.spec\.js$/,
    },

    /* Accessibility testing project */
    {
      name: 'accessibility',
      use: { ...devices['Desktop Chrome'] },
      testMatch: /.*\/accessibility\/.*\.spec\.js$/,
    },
  ],

  /* Run your local dev server before starting the tests */
  webServer: [
    {
      command: 'npm run dev',
      url: process.env.FRONTEND_URL || 'http://localhost:5173',
      reuseExistingServer: !process.env.CI,
      timeout: 180 * 1000, // Increased timeout to 3 minutes
      stdout: 'pipe',
      stderr: 'pipe',
    },
    {
      command: process.platform === 'win32'
        ? 'cd ..\\backend && python app.py'
        : 'cd ../backend && python app.py',
      url: process.env.BACKEND_URL || 'http://localhost:5000',
      reuseExistingServer: !process.env.CI,
      timeout: 180 * 1000, // Increased timeout to 3 minutes
      stdout: 'pipe',
      stderr: 'pipe',
      env: {
        ...process.env,
        FLASK_ENV: 'development',
        PYTHONUNBUFFERED: '1'
      }
    }
  ],

  /* Global setup and teardown */
  globalSetup: './tests/e2e/global-setup.js',
  globalTeardown: './tests/e2e/global-teardown.js',

  /* Test timeout */
  timeout: 30 * 1000,
  expect: {
    /* Timeout for expect() assertions */
    timeout: 5000,
  },

  /* Output directory for test artifacts */
  outputDir: 'test-results/',
});
